# 川味小厨餐饮小程序

## 项目简介

川味小厨是一款专注于堂食点餐的微信小程序，为川菜餐厅提供数字化点餐解决方案。通过扫码或手动输入桌号的方式，顾客可以便捷地浏览菜单、下单点餐，享受优质的用餐体验。

## 功能特色

### 🏠 首页
- **店铺信息展示**: 餐厅名称、描述、联系方式
- **桌号选择**: 支持扫码选桌和手动输入
- **今日推荐**: 展示特色菜品，吸引顾客
- **订单状态**: 实时显示当前订单进度

### 🍽️ 菜单页
- **分类浏览**: 热菜、凉菜、汤品、饮品、主食
- **菜品详情**: 高清图片、详细描述、价格信息
- **推荐标签**: 突出显示招牌菜品
- **购物车**: 实时计算总价，支持数量调整
- **下单功能**: 一键下单，支持备注

### 👤 个人页
- **用户信息**: 头像、昵称、会员等级
- **订单管理**: 当前订单、历史订单查看
- **优惠券**: 查看可用优惠券
- **会员服务**: 邀请好友、购买会员
- **设置功能**: 数据清除、关于我们

## 技术特点

### 前端技术
- **框架**: 微信小程序原生开发
- **语言**: TypeScript + WXML + WXSS
- **设计**: 响应式布局，川菜主题色彩
- **交互**: 流畅的用户体验，简约的操作流程

### 数据管理
- **本地存储**: 使用 wx.storage 管理用户数据
- **状态管理**: 页面间数据同步
- **模拟数据**: 丰富的假数据展示效果
- **扩展性**: 预留后端接口集成能力

### 设计理念
- **简约高效**: 3页面设计，减少操作步骤
- **川菜特色**: 橙红色主题，体现川菜文化
- **用户友好**: 直观的界面，清晰的信息层次
- **移动优先**: 专为手机端优化的交互体验

## 项目结构

```
miniprogram/
├── pages/              # 页面文件
│   ├── home/          # 首页
│   ├── menu/          # 菜单页
│   └── profile/       # 个人页
├── images/            # 图片资源
│   ├── categories/    # 分类图标
│   └── dishes/        # 菜品图片
├── utils/             # 工具函数
├── docs/              # 文档
└── typings/           # 类型定义
```

## 快速开始

### 环境要求
- 微信开发者工具
- Node.js (可选，用于后端开发)

### 运行步骤
1. 下载微信开发者工具
2. 导入项目到开发者工具
3. 配置小程序 AppID
4. 点击编译运行

### 开发说明
- 所有图片文件为占位文件，实际使用时需要替换为真实图片
- 数据为模拟数据，可根据需要连接真实后端API
- 样式采用rpx单位，自动适配不同屏幕尺寸

## 数据说明

### 模拟数据包含
- **店铺信息**: 川味小厨基本信息
- **菜品数据**: 16道川菜，包含5个分类
- **用户信息**: 默认用户"川菜达人"
- **订单记录**: 3条历史订单记录
- **优惠券**: 4张不同类型的优惠券

### 数据特点
- 真实的川菜菜品名称和价格
- 丰富的菜品描述和分类
- 完整的订单流程数据
- 多样化的优惠券类型

## 扩展功能

### 已实现功能
✅ 桌号管理  
✅ 菜品浏览  
✅ 购物车管理  
✅ 订单生成  
✅ 用户系统  
✅ 优惠券系统  

### 可扩展功能
🔄 后端API集成  
🔄 微信支付  
🔄 实时订单状态  
🔄 会员积分系统  
🔄 营销活动  
🔄 数据统计分析  

## 联系方式

如有问题或建议，欢迎联系：
- 项目地址: [GitHub仓库链接]
- 技术支持: [联系邮箱]
- 商务合作: [商务邮箱]

---

*川味小厨小程序 v1.0*  
*专注川菜，品味正宗*

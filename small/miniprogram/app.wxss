/**app.wxss**/
/* 全局样式 - 高端餐饮风格 */
page {
  background-color: #FEFEFE; /* 奶白色背景，更加优雅 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #2C3E50; /* 深色文字，提升可读性 */
}

/* 高端餐饮色彩系统 */
.primary-color { color: #27AE60; } /* 深绿色 - 自然品质 */
.primary-bg { background-color: #27AE60; }
.secondary-color { color: #F1C40F; } /* 金色 - 高端点缀 */
.accent-color { color: #E8F5E8; } /* 浅绿色 - 辅助色 */
.member-color { color: #F39C12; } /* 温暖金色 - 会员专属 */
.success-color { color: #27AE60; } /* 统一使用主色调 */
.text-gray { color: #7F8C8D; } /* 柔和灰色 */
.text-dark { color: #2C3E50; } /* 深色文字 */
.text-light { color: #95A5A6; } /* 浅灰色文字 */

/* 通用容器 - 简约设计 */
.container {
  padding: 24rpx;
  background-color: #FFFFFF;
  margin-bottom: 16rpx;
  border-radius: 12rpx; /* 更加现代的圆角 */
  box-shadow: 0 2rpx 12rpx rgba(44, 62, 80, 0.08); /* 柔和阴影 */
}

/* 按钮样式 - 高端设计 */
.btn {
  border-radius: 8rpx; /* 减少圆角，更加现代 */
  font-size: 30rpx;
  font-weight: 500; /* 减轻字重 */
  border: none;
  transition: all 0.3s ease; /* 添加过渡效果 */
  letter-spacing: 0.5rpx; /* 字间距优化 */
}

.btn-primary {
  background-color: #27AE60;
  color: #FFFFFF;
  box-shadow: 0 4rpx 16rpx rgba(39, 174, 96, 0.3);
}

.btn-primary:active {
  background-color: #229954;
  transform: translateY(1rpx); /* 微妙的按压效果 */
}

.btn-secondary {
  background-color: #F1C40F;
  color: #2C3E50; /* 深色文字在金色背景上更清晰 */
  box-shadow: 0 4rpx 16rpx rgba(241, 196, 15, 0.3);
}

.btn-secondary:active {
  background-color: #F4D03F;
}

/* 卡片样式 - 现代简约 */
.card {
  background-color: #FFFFFF;
  border-radius: 12rpx; /* 统一圆角设计 */
  padding: 32rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 16rpx rgba(44, 62, 80, 0.06); /* 更柔和的阴影 */
  border: 1rpx solid #F8F9FA; /* 添加细边框增加层次 */
}

/* 列表项 - 优化间距 */
.list-item {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #ECF0F1; /* 更柔和的分割线 */
  transition: background-color 0.2s ease;
}

.list-item:hover {
  background-color: #F8F9FA; /* 悬停效果 */
}

.list-item:last-child {
  border-bottom: none;
}

/* 文本样式 - 层次分明 */
.title {
  font-size: 34rpx;
  font-weight: 600; /* 适中的字重 */
  color: #2C3E50;
  line-height: 1.4;
}

.subtitle {
  font-size: 26rpx;
  color: #7F8C8D;
  line-height: 1.5;
}

.price {
  font-size: 32rpx;
  font-weight: 600;
  color: #27AE60; /* 使用主色调 */
}

/* 徽章 - 精致设计 */
.badge {
  background-color: #F1C40F; /* 金色徽章更显高端 */
  color: #2C3E50;
  font-size: 20rpx;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 12rpx; /* 统一圆角风格 */
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  box-shadow: 0 2rpx 8rpx rgba(241, 196, 15, 0.3);
}

/* 状态徽章变体 */
.badge.success {
  background-color: #27AE60;
  color: #FFFFFF;
}

.badge.info {
  background-color: #3498DB;
  color: #FFFFFF;
}

.badge.warning {
  background-color: #F39C12;
  color: #FFFFFF;
}

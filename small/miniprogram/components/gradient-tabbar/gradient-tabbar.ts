// components/gradient-tabbar/gradient-tabbar.ts
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前选中的tab索引
    currentTab: {
      type: Number,
      value: 0
    },
    // 主题样式: classic, rainbow, dark, glass
    theme: {
      type: String,
      value: 'classic'
    },
    // tab列表配置
    tabList: {
      type: Array,
      value: [
        {
          pagePath: 'pages/home/<USER>',
          text: '首页'
        },
        {
          pagePath: 'pages/menu/menu',
          text: '菜单'
        },
        {
          pagePath: 'pages/profile/profile',
          text: '个人'
        }
      ]
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击tab项
     */
    onTabClick(e: any) {
      const index = e.currentTarget.dataset.index;
      const tabItem = this.data.tabList[index];
      
      if (index === this.data.currentTab) {
        return; // 如果点击的是当前tab，不做处理
      }

      // 触发自定义事件，通知父组件tab切换
      this.triggerEvent('tabchange', {
        index: index,
        tabItem: tabItem
      });

      // 页面跳转
      if (tabItem.pagePath) {
        wx.switchTab({
          url: `/${tabItem.pagePath}`
        });
      }
    },

    /**
     * 设置当前选中的tab
     */
    setCurrentTab(index: number) {
      this.setData({
        currentTab: index
      });
    },

    /**
     * 更新tab列表
     */
    updateTabList(tabList: any[]) {
      this.setData({
        tabList: tabList
      });
    },

    /**
     * 切换主题
     */
    setTheme(theme: string) {
      this.setData({
        theme: theme
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被创建好时执行
      console.log('gradient-tabbar attached');
    },

    detached() {
      // 组件实例被从页面节点树移除时执行
      console.log('gradient-tabbar detached');
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 组件所在的页面被展示时执行
      this.updateCurrentTab();
    },

    hide() {
      // 组件所在的页面被隐藏时执行
    }
  },

  /**
   * 其他方法
   */
  updateCurrentTab() {
    // 根据当前页面路径更新选中状态
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage.route;
      
      const tabList = this.data.tabList;
      const currentIndex = tabList.findIndex((item: any) => item.pagePath === currentRoute);
      
      if (currentIndex !== -1 && currentIndex !== this.data.currentTab) {
        this.setData({
          currentTab: currentIndex
        });
      }
    }
  }
});

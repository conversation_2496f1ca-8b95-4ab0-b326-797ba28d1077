<view class="gradient-tabbar {{theme}}">
  <view class="tabbar-background"></view>
  <view class="tabbar-content">
    <view 
      wx:for="{{tabList}}" 
      wx:key="index"
      class="tab-item {{currentTab === index ? 'active' : ''}}"
      data-index="{{index}}"
      bindtap="onTabClick"
    >
      <view class="tab-icon">
        <image 
          wx:if="{{currentTab === index && item.selectedIconPath}}"
          src="{{item.selectedIconPath}}" 
          class="icon-image"
        />
        <image 
          wx:else
          src="{{item.iconPath}}" 
          class="icon-image"
        />
      </view>
      <text class="tab-text">{{item.text}}</text>
      <view wx:if="{{currentTab === index}}" class="active-indicator"></view>
    </view>
  </view>
</view>

# 川味小厨餐饮小程序 - 后端接口设计文档

## 项目背景

### 项目概述
川味小厨是一款专注于堂食点餐的微信小程序，为川菜餐厅提供数字化点餐解决方案。通过扫码或手动输入桌号的方式，顾客可以便捷地浏览菜单、下单点餐，享受优质的用餐体验。

### 目标用户
- **主要用户**: 到店用餐的顾客
- **使用场景**: 堂食点餐、会员服务、订单管理、微信支付
- **核心需求**: 快速点餐、套餐选择、会员优惠、返现抵扣、订单跟踪

### 设计理念
- **简约高效**: 3页面设计，减少操作步骤
- **川菜特色**: 橙红色主题，体现川菜文化
- **会员导向**: 完整的会员体系和拉新返现机制
- **数据驱动**: 支持后端数据管理和实时更新

## 技术架构

### 前端技术栈
- **框架**: 微信小程序原生开发
- **语言**: TypeScript + WXML + WXSS
- **状态管理**: 本地存储 + 页面数据绑定
- **UI设计**: 响应式布局，适配多种屏幕
- **支付**: 微信支付 JSAPI

### 后端技术栈
- **语言**: Java 17
- **框架**: Spring Boot 3.x
- **数据库**: Supabase (PostgreSQL)
- **认证**: 微信小程序登录 + JWT
- **文件存储**: Supabase Storage
- **支付**: 微信支付 API v3

## 核心功能模块

### 1. 桌号管理
- 扫码选桌 (二维码包含桌号信息)
- 手动输入桌号
- 桌号状态管理 (空闲/占用/清理中)

### 2. 菜品管理
- 分类浏览 (热菜、凉菜、汤品、饮品、主食等10个分类)
- 菜品详情展示 (价格、描述、图片、推荐标识)
- 套餐管理 (套餐组合、优惠价格)
- 库存状态管理
- 价格动态调整

### 3. 购物车与订单
- 购物车商品管理 (单品+套餐)
- 订单生成与微信支付
- 返现金额抵扣
- 订单状态跟踪
- 历史订单查询

### 4. 会员系统
- 会员等级管理 (普通/铜牌/银牌/金牌)
- 优惠券发放与使用
- 会员拉新返现机制 (仅会员可参与)
- 返现余额管理 (支付时抵扣)

### 5. 店铺管理
- 店铺基本信息
- 营业时间设置
- 公告管理
- 推荐菜品设置

### 6. 支付系统
- 微信支付集成
- 支付回调处理
- 返现抵扣计算
- 支付状态管理

## 数据库设计 (MySQL语法，部署时转换为PostgreSQL)

### 核心业务表 (带small_前缀)

#### 1. 店铺信息表 (small_shops)
```sql
CREATE TABLE small_shops (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '店铺名称',
  description VARCHAR(200) COMMENT '店铺描述',
  phone VARCHAR(20) COMMENT '联系电话',
  address VARCHAR(200) COMMENT '店铺地址',
  business_hours VARCHAR(50) COMMENT '营业时间',
  logo_url VARCHAR(200) COMMENT 'Logo图片URL',
  status TINYINT DEFAULT 1 COMMENT '营业状态 1:营业 0:休息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 桌号管理表 (small_tables)
```sql
CREATE TABLE small_tables (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  table_number VARCHAR(10) NOT NULL UNIQUE COMMENT '桌号',
  seats INT DEFAULT 4 COMMENT '座位数',
  status TINYINT DEFAULT 0 COMMENT '状态 0:空闲 1:占用 2:清理中',
  qr_code VARCHAR(200) COMMENT '二维码内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 3. 菜品分类表 (small_categories)
```sql
CREATE TABLE small_categories (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL COMMENT '分类名称',
  icon_url VARCHAR(200) COMMENT '分类图标URL',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  status TINYINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 4. 菜品信息表 (small_dishes)
```sql
CREATE TABLE small_dishes (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  category_id BIGINT NOT NULL COMMENT '分类ID',
  name VARCHAR(100) NOT NULL COMMENT '菜品名称',
  description TEXT COMMENT '菜品描述',
  price DECIMAL(10,2) NOT NULL COMMENT '价格',
  image_url VARCHAR(200) COMMENT '菜品图片URL',
  stock INT DEFAULT -1 COMMENT '库存数量 -1:无限制',
  is_recommended TINYINT DEFAULT 0 COMMENT '是否推荐 1:是 0:否',
  status TINYINT DEFAULT 1 COMMENT '状态 1:可售 0:下架',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES small_categories(id)
);
```

#### 5. 套餐信息表 (small_meal_sets)
```sql
CREATE TABLE small_meal_sets (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '套餐名称',
  description TEXT COMMENT '套餐描述',
  original_price DECIMAL(10,2) NOT NULL COMMENT '原价',
  set_price DECIMAL(10,2) NOT NULL COMMENT '套餐价',
  image_url VARCHAR(200) COMMENT '套餐图片URL',
  is_recommended TINYINT DEFAULT 0 COMMENT '是否推荐 1:是 0:否',
  status TINYINT DEFAULT 1 COMMENT '状态 1:可售 0:下架',
  sort_order INT DEFAULT 0 COMMENT '排序权重',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 6. 套餐菜品关联表 (small_meal_set_dishes)
```sql
CREATE TABLE small_meal_set_dishes (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  meal_set_id BIGINT NOT NULL COMMENT '套餐ID',
  dish_id BIGINT NOT NULL COMMENT '菜品ID',
  quantity INT DEFAULT 1 COMMENT '数量',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (meal_set_id) REFERENCES small_meal_sets(id),
  FOREIGN KEY (dish_id) REFERENCES small_dishes(id)
);
```

#### 7. 用户信息表 (small_users)
```sql
CREATE TABLE small_users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  openid VARCHAR(100) NOT NULL UNIQUE COMMENT '微信OpenID',
  unionid VARCHAR(100) COMMENT '微信UnionID',
  nickname VARCHAR(100) COMMENT '昵称',
  avatar_url VARCHAR(200) COMMENT '头像URL',
  phone VARCHAR(20) COMMENT '手机号',
  member_level TINYINT DEFAULT 0 COMMENT '会员等级 0:普通 1:铜牌 2:银牌 3:金牌',
  member_expire_date DATE COMMENT '会员到期日期',
  cashback_balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '返现余额',
  invite_code VARCHAR(20) UNIQUE COMMENT '邀请码',
  inviter_id BIGINT COMMENT '邀请人ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_invite_code (invite_code),
  INDEX idx_inviter_id (inviter_id),
  FOREIGN KEY (inviter_id) REFERENCES small_users(id)
);
```

#### 8. 订单主表 (small_orders)
```sql
CREATE TABLE small_orders (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
  user_id BIGINT NOT NULL COMMENT '用户ID',
  table_number VARCHAR(10) NOT NULL COMMENT '桌号',
  total_amount DECIMAL(10,2) NOT NULL COMMENT '订单总金额',
  discount_amount DECIMAL(10,2) DEFAULT 0 COMMENT '优惠金额',
  cashback_used DECIMAL(10,2) DEFAULT 0 COMMENT '使用的返现金额',
  final_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
  status VARCHAR(20) DEFAULT 'unpaid' COMMENT '订单状态 unpaid:待支付 pending:待处理 cooking:制作中 completed:已完成 cancelled:已取消',
  payment_status TINYINT DEFAULT 0 COMMENT '支付状态 0:未支付 1:已支付 2:支付失败',
  payment_method VARCHAR(20) COMMENT '支付方式',
  wechat_prepay_id VARCHAR(100) COMMENT '微信预支付ID',
  wechat_transaction_id VARCHAR(100) COMMENT '微信交易号',
  remark TEXT COMMENT '订单备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_payment_status (payment_status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES small_users(id)
);
```

#### 9. 订单详情表 (small_order_items)
```sql
CREATE TABLE small_order_items (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  order_id BIGINT NOT NULL COMMENT '订单ID',
  item_type TINYINT NOT NULL COMMENT '商品类型 1:单品 2:套餐',
  dish_id BIGINT COMMENT '菜品ID',
  meal_set_id BIGINT COMMENT '套餐ID',
  item_name VARCHAR(100) NOT NULL COMMENT '商品名称',
  item_price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
  quantity INT NOT NULL COMMENT '数量',
  subtotal DECIMAL(10,2) NOT NULL COMMENT '小计金额',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_order_id (order_id),
  FOREIGN KEY (order_id) REFERENCES small_orders(id),
  FOREIGN KEY (dish_id) REFERENCES small_dishes(id),
  FOREIGN KEY (meal_set_id) REFERENCES small_meal_sets(id)
);
```

#### 10. 优惠券表 (small_coupons)
```sql
CREATE TABLE small_coupons (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
  type TINYINT NOT NULL COMMENT '类型 1:满减券 2:折扣券 3:新用户券',
  amount DECIMAL(10,2) NOT NULL COMMENT '优惠金额/折扣率',
  min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最低消费金额',
  total_count INT NOT NULL COMMENT '发放总数',
  used_count INT DEFAULT 0 COMMENT '已使用数量',
  expire_days INT NOT NULL COMMENT '有效天数',
  status TINYINT DEFAULT 1 COMMENT '状态 1:有效 0:无效',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 11. 用户优惠券表 (small_user_coupons)
```sql
CREATE TABLE small_user_coupons (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL COMMENT '用户ID',
  coupon_id BIGINT NOT NULL COMMENT '优惠券ID',
  coupon_name VARCHAR(100) NOT NULL COMMENT '优惠券名称',
  amount DECIMAL(10,2) NOT NULL COMMENT '优惠金额',
  min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最低消费金额',
  status TINYINT DEFAULT 1 COMMENT '状态 1:未使用 2:已使用 3:已过期',
  expire_date DATE NOT NULL COMMENT '过期日期',
  used_at TIMESTAMP NULL COMMENT '使用时间',
  order_id BIGINT COMMENT '使用订单ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  FOREIGN KEY (user_id) REFERENCES small_users(id),
  FOREIGN KEY (coupon_id) REFERENCES small_coupons(id),
  FOREIGN KEY (order_id) REFERENCES small_orders(id)
);
```

#### 12. 返现记录表 (small_cashback_records)
```sql
CREATE TABLE small_cashback_records (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT NOT NULL COMMENT '获得返现的会员ID',
  invitee_id BIGINT NOT NULL COMMENT '被邀请的用户ID',
  order_id BIGINT NOT NULL COMMENT '触发返现的订单ID',
  amount DECIMAL(10,2) NOT NULL COMMENT '返现金额',
  status TINYINT DEFAULT 1 COMMENT '状态 1:已发放 0:已撤销',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_invitee_id (invitee_id),
  FOREIGN KEY (user_id) REFERENCES small_users(id),
  FOREIGN KEY (invitee_id) REFERENCES small_users(id),
  FOREIGN KEY (order_id) REFERENCES small_orders(id)
);
```

#### 13. 会员等级配置表 (small_member_levels)
```sql
CREATE TABLE small_member_levels (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  level_code TINYINT NOT NULL UNIQUE COMMENT '等级代码 0:普通 1:铜牌 2:银牌 3:金牌',
  level_name VARCHAR(50) NOT NULL COMMENT '等级名称',
  annual_fee DECIMAL(10,2) DEFAULT 0 COMMENT '年费',
  cashback_rate DECIMAL(5,4) DEFAULT 0 COMMENT '返现比例',
  invite_reward DECIMAL(10,2) DEFAULT 0 COMMENT '邀请奖励金额',
  benefits TEXT COMMENT '会员权益描述',
  status TINYINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 系统配置表 (无前缀)

#### 14. 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
  config_value TEXT COMMENT '配置值',
  description VARCHAR(200) COMMENT '配置描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 15. 公告信息表 (announcements)
```sql
CREATE TABLE announcements (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL COMMENT '公告标题',
  content TEXT COMMENT '公告内容',
  type TINYINT DEFAULT 1 COMMENT '公告类型 1:系统公告 2:活动公告 3:维护公告',
  priority TINYINT DEFAULT 1 COMMENT '优先级 1:低 2:中 3:高',
  status TINYINT DEFAULT 1 COMMENT '状态 1:发布 0:草稿',
  start_time TIMESTAMP COMMENT '开始时间',
  end_time TIMESTAMP COMMENT '结束时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 业务模块梳理

### 1. 用户认证模块
**功能**: 微信小程序登录、用户信息管理、邀请码验证
**涉及表**: `small_users`, `small_member_levels`
**核心流程**:
- 微信小程序登录获取code
- 后端调用微信API获取openid
- 创建/更新用户信息
- 处理邀请码关系
- 生成自定义JWT Token

### 2. 店铺信息模块
**功能**: 店铺基础信息、推荐菜品、推荐套餐
**涉及表**: `small_shops`, `small_dishes`, `small_meal_sets`
**核心流程**:
- 获取店铺营业信息
- 展示推荐菜品列表
- 展示推荐套餐列表

### 3. 桌号管理模块
**功能**: 桌号验证、占用、释放
**涉及表**: `small_tables`
**核心流程**:
- 验证桌号有效性
- 更新桌号占用状态
- 处理桌号释放

### 4. 菜品管理模块
**功能**: 分类管理、菜品展示、套餐管理
**涉及表**: `small_categories`, `small_dishes`, `small_meal_sets`, `small_meal_set_dishes`
**核心流程**:
- 获取菜品分类列表
- 按分类获取菜品
- 获取套餐及组合菜品
- 库存状态管理

### 5. 订单管理模块
**功能**: 订单创建、支付、状态跟踪、补支付
**涉及表**: `small_orders`, `small_order_items`
**核心流程**:
- 创建订单(单品+套餐)
- 计算优惠和返现抵扣
- 生成微信支付参数
- 支付状态和订单状态分离管理
- 未支付订单支持补支付
- 订单列表按支付状态和时间排序

### 6. 会员系统模块
**功能**: 会员等级、优惠券、返现管理
**涉及表**: `small_users`, `small_member_levels`, `small_coupons`, `small_user_coupons`, `small_cashback_records`
**核心流程**:
- 会员等级管理
- 优惠券发放和使用
- 拉新返现计算
- 返现余额管理

### 7. 支付系统模块
**功能**: 微信支付、支付回调、退款、补支付
**涉及表**: `small_orders`
**核心流程**:
- 订单创建时生成微信支付参数
- 未支付订单支持重新发起支付
- 支付回调处理（更新支付状态和订单状态）
- 返现发放逻辑（仅首次支付成功时）
- 退款处理

### 8. 系统配置模块
**功能**: 系统参数配置、公告管理
**涉及表**: `system_configs`, `announcements`
**核心流程**:
- 系统参数管理
- 公告发布和展示

## API接口设计

### 基础配置
- **Base URL**: `https://api.chuanweixiaochu.com/api/v1`
- **认证方式**: 自定义JWT Token (通过微信小程序登录获取)
- **数据格式**: JSON
- **字符编码**: UTF-8
- **支付**: 微信支付 API v3

### 统一响应格式
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 错误码定义
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权/Token无效
- `403`: 禁止访问
- `404`: 资源不存在
- `409`: 资源冲突
- `422`: 业务逻辑错误
- `500`: 服务器内部错误

### 请求头配置
```
Authorization: Bearer {custom_jwt_token}
Content-Type: application/json
```

### 1. 用户认证接口

#### 1.1 微信登录
```
POST /auth/wechat-login
```

**说明**: 小程序调用wx.login()获取code，然后调用此接口完成登录

**请求参数:**
```json
{
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL"
  },
  "inviteCode": "邀请码(可选)"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "custom_jwt_token",
    "userInfo": {
      "id": 1,
      "nickName": "用户昵称",
      "avatarUrl": "头像URL",
      "memberLevel": 1,
      "memberLevelName": "铜牌会员",
      "memberExpireDate": "2024-12-31",
      "cashbackBalance": 128.50,
      "inviteCode": "ABC123",
      "inviteCount": 3
    }
  }
}
```

**说明**:
- 后端收到code后调用微信API获取openid
- 根据openid查询或创建用户
- 处理邀请码逻辑
- 返回自定义JWT token和用户信息

### 2. 店铺信息接口

#### 2.1 获取店铺信息
```
GET /shop/info
```

**说明**: 获取店铺基本信息，对应小程序home页面的shopInfo数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "name": "川味小厨",
    "description": "正宗川菜 · 地道美味",
    "phone": "023-8888-6666",
    "address": "重庆市渝中区美食街88号",
    "businessHours": "09:00 - 22:00",
    "logo": "https://supabase-storage-url/logo.png",
    "status": 1
  }
}
```

#### 2.2 获取推荐菜品
```
GET /shop/recommended-dishes
```

**说明**: 获取首页推荐菜品列表，对应小程序home页面的recommendList数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "宫保鸡丁",
      "price": 28,
      "emoji": "🍗",
      "description": "经典川菜，鸡肉嫩滑，花生香脆",
      "image": "/images/dishes/gongbao.jpg"
    },
    {
      "id": 4,
      "name": "水煮鱼",
      "price": 45,
      "emoji": "🐟",
      "description": "鲜嫩鱼片，麻辣鲜香",
      "image": "/images/dishes/shuizhuyu.jpg"
    }
  ]
}
```

### 3. 桌号管理接口

#### 3.1 验证桌号
```
POST /tables/validate
```

**请求参数:**
```json
{
  "tableNumber": "A01"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "valid": true,
    "tableInfo": {
      "id": 1,
      "tableNumber": "A01",
      "seats": 4,
      "status": 0
    }
  }
}
```

#### 3.2 占用桌号
```
POST /tables/occupy
```

**请求参数:**
```json
{
  "tableNumber": "A01"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "message": "桌号占用成功"
}
```

#### 3.3 释放桌号
```
POST /tables/release
```

**请求参数:**
```json
{
  "tableNumber": "A01"
}
```

### 4. 菜品管理接口

#### 4.1 获取菜品分类
```
GET /dishes/categories
```

**说明**: 获取菜品分类列表，对应小程序menu页面的categories数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "热菜",
      "emoji": "🔥",
      "icon": "/images/categories/hot.png"
    },
    {
      "id": 2,
      "name": "凉菜",
      "emoji": "🥗",
      "icon": "/images/categories/cold.png"
    },
    {
      "id": 3,
      "name": "汤品",
      "emoji": "🍲",
      "icon": "/images/categories/soup.png"
    }
  ]
}
```

#### 4.2 获取所有菜品
```
GET /dishes/list
```

**说明**: 获取所有菜品数据，对应小程序menu页面的dishes数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "宫保鸡丁",
      "description": "经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣",
      "price": 28,
      "emoji": "🍗",
      "image": "/images/dishes/gongbao.jpg",
      "categoryId": 1,
      "isRecommended": true,
      "count": 0
    },
    {
      "id": 2,
      "name": "麻婆豆腐",
      "description": "嫩滑豆腐配麻辣肉末，口感丰富层次分明",
      "price": 18,
      "emoji": "🥘",
      "image": "/images/dishes/mapo.jpg",
      "categoryId": 1,
      "count": 0
    }
  ]
}
```

#### 4.3 获取套餐列表
```
GET /meal-sets/list?page=1&limit=20
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "经典川菜套餐",
        "description": "宫保鸡丁+麻婆豆腐+白米饭",
        "originalPrice": 68.00,
        "setPrice": 58.00,
        "imageUrl": "https://supabase-storage-url/meal-sets/classic.jpg",
        "isRecommended": true,
        "status": 1,
        "dishes": [
          {"id": 1, "name": "宫保鸡丁", "quantity": 1},
          {"id": 2, "name": "麻婆豆腐", "quantity": 1},
          {"id": 15, "name": "白米饭", "quantity": 2}
        ]
      }
    ],
    "total": 10,
    "page": 1,
    "limit": 20
  }
}
```

#### 4.4 获取套餐详情
```
GET /meal-sets/{id}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "id": 1,
    "name": "经典川菜套餐",
    "description": "宫保鸡丁+麻婆豆腐+白米饭",
    "originalPrice": 68.00,
    "setPrice": 58.00,
    "imageUrl": "https://supabase-storage-url/meal-sets/classic.jpg",
    "dishes": [
      {
        "id": 1,
        "name": "宫保鸡丁",
        "price": 28.00,
        "quantity": 1,
        "imageUrl": "https://supabase-storage-url/dishes/gongbao.jpg"
      },
      {
        "id": 2,
        "name": "麻婆豆腐",
        "price": 18.00,
        "quantity": 1,
        "imageUrl": "https://supabase-storage-url/dishes/mapo.jpg"
      },
      {
        "id": 15,
        "name": "白米饭",
        "price": 3.00,
        "quantity": 2,
        "imageUrl": "https://supabase-storage-url/dishes/rice.jpg"
      }
    ]
  }
}
```

### 5. 订单管理接口

#### 5.1 创建订单
```
POST /orders/create
```

**说明**: 根据小程序购物车数据创建订单，对应menu页面的checkout()方法

**请求参数:**
```json
{
  "tableNumber": "A01",
  "items": [
    {
      "id": 1,
      "name": "宫保鸡丁",
      "description": "经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣",
      "price": 28,
      "emoji": "🍗",
      "image": "/images/dishes/gongbao.jpg",
      "categoryId": 1,
      "isRecommended": true,
      "count": 2
    }
  ],
  "totalPrice": 56,
  "couponId": 1,
  "cashbackUsed": 20.00,
  "remark": "不要辣"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderId": 1,
    "orderNumber": "20241201123456001",
    "totalAmount": 56.00,
    "discountAmount": 10.00,
    "cashbackUsed": 20.00,
    "finalAmount": 26.00,
    "paymentInfo": {
      "prepayId": "wx_prepay_id_123456",
      "timeStamp": "1640995200",
      "nonceStr": "random_string_abc",
      "package": "prepay_id=wx_prepay_id_123456",
      "signType": "RSA",
      "paySign": "signature_hash"
    }
  }
}
```

**说明:**
- items数组直接使用小程序购物车的cartItems数据结构
- 后端根据菜品ID和数量计算总价
- 返回微信支付所需的参数

#### 5.2 获取订单列表
```
GET /orders/list
```

**说明**: 获取用户所有订单列表，按时间倒序，未支付订单置顶，对应profile页面的allOrders数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": "order_1",
      "orderNumber": "241201123456",
      "tableNumber": "A01",
      "items": [
        {
          "name": "宫保鸡丁",
          "count": 2,
          "price": 28
        }
      ],
      "totalPrice": 56,
      "finalAmount": 46,
      "status": "unpaid",
      "statusText": "待支付",
      "paymentStatus": 0,
      "createTime": 1640995200000,
      "createTimeText": "12-01 12:34",
      "dishNames": "宫保鸡丁×2"
    },
    {
      "id": "order_2",
      "orderNumber": "241130123456",
      "tableNumber": "B02",
      "items": [
        {
          "name": "麻婆豆腐",
          "count": 1,
          "price": 18
        }
      ],
      "totalPrice": 18,
      "finalAmount": 18,
      "status": "completed",
      "statusText": "已完成",
      "paymentStatus": 1,
      "createTime": 1640909200000,
      "createTimeText": "11-30 15:20",
      "dishNames": "麻婆豆腐×1"
    }
  ]
}
```

**说明:**
- 返回所有订单，未支付订单在前，已支付订单按时间倒序
- `status`: unpaid=待支付, pending=待处理, cooking=制作中, completed=已完成
- `paymentStatus`: 0=未支付, 1=已支付, 2=支付失败
- `finalAmount`: 实际支付金额（扣除优惠和返现后）

#### 5.3 支付未支付订单
```
POST /orders/{orderId}/pay
```

**说明**: 对未支付订单进行支付，对应profile页面的"去支付"按钮

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderId": 1,
    "orderNumber": "20241201123456001",
    "finalAmount": 46.00,
    "paymentInfo": {
      "prepayId": "wx_prepay_id_123456",
      "timeStamp": "1640995200",
      "nonceStr": "random_string_abc",
      "package": "prepay_id=wx_prepay_id_123456",
      "signType": "RSA",
      "paySign": "signature_hash"
    }
  }
}
```

#### 5.4 获取订单详情
```
GET /orders/{orderId}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "id": 1,
    "orderNumber": "20241201123456001",
    "tableNumber": "A01",
    "totalAmount": 56.00,
    "discountAmount": 10.00,
    "cashbackUsed": 0.00,
    "finalAmount": 46.00,
    "status": "unpaid",
    "statusText": "待支付",
    "paymentStatus": 0,
    "paymentMethod": null,
    "wechatTransactionId": null,
    "remark": "不要辣",
    "createdAt": "2024-12-01T12:34:56Z",
    "items": [
      {
        "id": 1,
        "itemName": "宫保鸡丁",
        "itemPrice": 28.00,
        "quantity": 2,
        "subtotal": 56.00
      }
    ]
  }
}
```

### 6. 会员系统接口

#### 6.1 获取用户信息
```
GET /users/profile
```

**说明**: 获取用户完整信息，对应profile页面的userInfo和memberInfo数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "userInfo": {
      "nickName": "川菜达人",
      "avatarUrl": "/images/default-avatar.png"
    },
    "memberInfo": {
      "isMember": true,
      "level": "gold",
      "levelName": "黄金会员",
      "expireDate": "2024.12.31"
    },
    "cashbackAmount": 128.50,
    "inviteCount": 3,
    "inviteCode": "ABC123"
  }
}
```

#### 6.2 获取用户优惠券
```
GET /users/coupons
```

**说明**: 获取用户优惠券列表，对应profile页面的coupons数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "新用户专享",
      "amount": 10,
      "minAmount": 50,
      "expireDate": "2024.12.31"
    },
    {
      "id": 2,
      "name": "会员专享",
      "amount": 20,
      "minAmount": 100,
      "expireDate": "2024.12.31"
    }
  ]
}
```

#### 6.3 获取邀请记录
```
GET /users/invite-records
```

**说明**: 获取用户邀请记录，对应profile页面的inviteList数据

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": [
    {
      "id": 1,
      "name": "张小明",
      "inviteTime": "2024-11-25 14:30",
      "reward": 50
    },
    {
      "id": 2,
      "name": "李小红",
      "inviteTime": "2024-11-20 09:15",
      "reward": 50
    },
    {
      "id": 3,
      "name": "王小华",
      "inviteTime": "2024-11-18 16:45",
      "reward": 50
    }
  ]
}
```

#### 6.4 购买会员
```
POST /users/purchase-membership
```

**请求参数:**
```json
{
  "memberLevel": 3,
  "duration": 12
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderId": 2,
    "orderNumber": "20241201123456002",
    "amount": 99.00,
    "paymentInfo": {
      "prepayId": "wx_prepay_id_membership",
      "timeStamp": "1640995200",
      "nonceStr": "random_string_def",
      "package": "prepay_id=wx_prepay_id_membership",
      "signType": "RSA",
      "paySign": "signature_hash_membership"
    }
  }
}
```

#### 6.5 验证邀请码
```
POST /users/validate-invite-code
```

**请求参数:**
```json
{
  "inviteCode": "ABC123"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "valid": true,
    "inviterInfo": {
      "nickname": "川菜达人",
      "memberLevel": 3
    }
  }
}
```

### 7. 支付系统接口

#### 7.1 微信支付回调
```
POST /payments/wechat/notify
```

**请求说明:**
- 微信支付服务器回调接口
- 需要验证微信签名
- 处理支付成功/失败状态
- 更新订单支付状态
- 处理会员拉新返现逻辑

**回调处理逻辑:**
1. 验证微信支付签名
2. 更新订单支付状态（payment_status = 1）
3. 更新订单状态（status = 'pending'，进入待处理状态）
4. 如果是新用户首次消费，给邀请人发放返现
5. 返回微信要求的响应格式

#### 7.2 查询支付状态
```
GET /payments/status/{orderNumber}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "orderNumber": "20241201123456001",
    "paymentStatus": 1,
    "paymentMethod": "wechat",
    "wechatTransactionId": "wx_transaction_123456",
    "paidAt": "2024-12-01T12:35:30Z"
  }
}
```

#### 7.3 申请退款
```
POST /payments/refund
```

**请求参数:**
```json
{
  "orderNumber": "20241201123456001",
  "refundAmount": 84.00,
  "reason": "用户取消订单"
}
```

**响应数据:**
```json
{
  "success": true,
  "code": 200,
  "data": {
    "refundId": "refund_123456",
    "refundStatus": "processing",
    "refundAmount": 84.00
  }
}
```

## 前端数据适配

### Supabase客户端配置
```typescript
// utils/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://your-project.supabase.co'
const supabaseAnonKey = 'your-anon-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### API服务封装
```typescript
// utils/api.ts
class ApiService {
  private baseURL = 'https://api.chuanweixiaochu.com/api/v1'

  private async request(url: string, options: any = {}) {
    const token = await this.getSupabaseToken()

    return wx.request({
      url: `${this.baseURL}${url}`,
      method: options.method || 'GET',
      data: options.data,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.header
      }
    })
  }

  private async getSupabaseToken() {
    const { data: { session } } = await supabase.auth.getSession()
    return session?.access_token || ''
  }

  // 微信登录
  async wechatLogin(code: string, userInfo: any, inviteCode?: string) {
    return this.request('/auth/wechat-login', {
      method: 'POST',
      data: { code, userInfo, inviteCode }
    })
  }

  // 获取店铺信息
  async getShopInfo() {
    return this.request('/shop/info')
  }

  // 创建订单
  async createOrder(orderData: any) {
    return this.request('/orders/create', {
      method: 'POST',
      data: orderData
    })
  }
}

export const apiService = new ApiService()
```

### 渐进式接口集成策略
1. **第一阶段**: 集成用户认证和基础数据接口
2. **第二阶段**: 集成菜品、套餐和购物车接口
3. **第三阶段**: 集成订单和支付接口
4. **第四阶段**: 集成会员系统和返现功能

## Spring Boot 后端实现要点

### 依赖配置 (pom.xml)
```xml
<dependencies>
    <!-- Spring Boot 3.x -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>

    <!-- Supabase PostgreSQL -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
    </dependency>

    <!-- 微信支付 -->
    <dependency>
        <groupId>com.github.wechatpay-apiv3</groupId>
        <artifactId>wechatpay-java</artifactId>
        <version>0.2.12</version>
    </dependency>

    <!-- JWT -->
    <dependency>
        <groupId>io.jsonwebtoken</groupId>
        <artifactId>jjwt-api</artifactId>
        <version>0.11.5</version>
    </dependency>
</dependencies>
```

### 核心配置
```yaml
# application.yml
spring:
  datasource:
    url: ***********************************************************
    username: postgres
    password: your-password
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false

supabase:
  url: https://your-project.supabase.co
  anon-key: your-anon-key
  service-role-key: your-service-role-key

wechat:
  pay:
    app-id: your-wechat-app-id
    mch-id: your-merchant-id
    private-key-path: classpath:wechat-pay-private-key.pem
    merchant-serial-number: your-serial-number
    api-v3-key: your-api-v3-key
```

## 部署建议

### 开发环境
- 使用Supabase本地开发环境
- 微信支付沙箱环境测试
- 本地Spring Boot应用调试

### 生产环境
- **后端**: 部署到云服务器 (阿里云/腾讯云)
- **数据库**: Supabase云端PostgreSQL
- **文件存储**: Supabase Storage
- **CDN**: 加速图片和静态资源
- **监控**: 集成应用性能监控
- **安全**: HTTPS + API限流 + 参数验证

## 扩展功能规划

### 短期扩展 (3个月内)
1. **营销活动**: 满减、折扣券、限时优惠
2. **数据统计**: 销售报表、热门菜品分析
3. **推送通知**: 订单状态变更通知

### 中期扩展 (6个月内)
1. **外卖配送**: 配送地址、配送费计算
2. **多店铺支持**: 连锁店管理
3. **会员积分**: 积分兑换、等级升级

### 长期扩展 (1年内)
1. **智能推荐**: 基于用户偏好的菜品推荐
2. **语音点餐**: 集成语音识别
3. **AR菜单**: 增强现实菜品展示

---

*本文档版本: v2.0*
*最后更新: 2024-12-01*
*技术栈: Java 17 + Spring Boot 3.x + Supabase*
*维护人员: 开发团队*

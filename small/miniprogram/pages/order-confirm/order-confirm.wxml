<!--订单确认页-->
<view class="order-confirm-container">
  <!-- 桌号信息 -->
  <view class="table-info">
    <text class="table-text">{{tableNumber}}号桌</text>
  </view>

  <!-- 订单商品列表 -->
  <view class="order-items-section">
    <view class="section-title">订单详情</view>
    <view class="items-list">
      <view class="item-row" wx:for="{{orderItems}}" wx:key="id">
        <view class="item-info">
          <text class="item-emoji">{{item.emoji}}</text>
          <view class="item-details">
            <text class="item-name">{{item.name}}</text>
            <text class="item-price">¥{{item.price}}</text>
          </view>
        </view>
        <view class="item-quantity">
          <text class="quantity-text">×{{item.count}}</text>
          <text class="subtotal-text">¥{{item.price * item.count}}</text>
        </view>
      </view>
    </view>
    
    <!-- 商品总计 -->
    <view class="items-summary">
      <text class="summary-text">共{{totalCount}}件商品</text>
      <text class="summary-amount">¥{{totalAmount}}</text>
    </view>
  </view>

  <!-- 优惠信息 -->
  <view class="discount-section">
    <!-- 优惠券 -->
    <view class="discount-row" bindtap="showCouponSelector">
      <view class="discount-label">
        <text class="label-text">优惠券</text>
        <text class="available-count" wx:if="{{availableCoupons.length > 0}}">{{availableCoupons.length}}张可用</text>
      </view>
      <view class="discount-value">
        <text class="value-text" wx:if="{{selectedCoupon}}">-¥{{selectedCoupon.amount}}</text>
        <text class="placeholder-text" wx:else>选择优惠券</text>
        <text class="arrow">></text>
      </view>
    </view>

    <!-- 返现抵扣 -->
    <view class="discount-row" bindtap="showCashbackSelector">
      <view class="discount-label">
        <text class="label-text">返现抵扣</text>
        <text class="balance-text">余额¥{{cashbackBalance}}</text>
      </view>
      <view class="discount-value">
        <text class="value-text" wx:if="{{cashbackUsed > 0}}">-¥{{cashbackUsed}}</text>
        <text class="placeholder-text" wx:else>使用返现</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 订单备注 -->
  <view class="remark-section">
    <view class="section-title">订单备注</view>
    <textarea 
      class="remark-input" 
      placeholder="请输入备注信息（如：不要辣、少盐等）"
      value="{{remark}}"
      bindinput="onRemarkInput"
      maxlength="100"
    ></textarea>
  </view>

  <!-- 价格明细 -->
  <view class="price-detail">
    <view class="detail-row">
      <text class="detail-label">商品总额</text>
      <text class="detail-value">¥{{totalAmount}}</text>
    </view>
    <view class="detail-row" wx:if="{{discountAmount > 0}}">
      <text class="detail-label">优惠券</text>
      <text class="detail-value discount">-¥{{discountAmount}}</text>
    </view>
    <view class="detail-row" wx:if="{{cashbackUsed > 0}}">
      <text class="detail-label">返现抵扣</text>
      <text class="detail-value discount">-¥{{cashbackUsed}}</text>
    </view>
    <view class="detail-row final">
      <text class="detail-label">实付金额</text>
      <text class="detail-value final-amount">¥{{finalAmount}}</text>
    </view>
  </view>

  <!-- 支付按钮 -->
  <view class="pay-section">
    <button 
      class="pay-button {{isCreatingOrder ? 'loading' : ''}}" 
      bindtap="createOrderAndPay"
      disabled="{{isCreatingOrder}}"
    >
      <text wx:if="{{isCreatingOrder}}">创建订单中...</text>
      <text wx:else>微信支付 ¥{{finalAmount}}</text>
    </button>
  </view>
</view>

<!-- 优惠券选择弹窗 -->
<view class="modal-overlay {{showCouponModal ? 'show' : ''}}" bindtap="hideCouponModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择优惠券</text>
      <text class="modal-close" bindtap="hideCouponModal">×</text>
    </view>
    <view class="coupon-list">
      <view class="coupon-item" wx:for="{{availableCoupons}}" wx:key="id" bindtap="selectCoupon" data-id="{{item.id}}">
        <view class="coupon-info">
          <text class="coupon-name">{{item.name}}</text>
          <text class="coupon-desc">满{{item.minAmount}}元可用</text>
        </view>
        <view class="coupon-amount">¥{{item.amount}}</view>
      </view>
      <view class="coupon-item" bindtap="removeCoupon" wx:if="{{selectedCoupon}}">
        <view class="coupon-info">
          <text class="coupon-name">不使用优惠券</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 返现使用弹窗 -->
<view class="modal-overlay {{showCashbackModal ? 'show' : ''}}" bindtap="hideCashbackModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">使用返现</text>
      <text class="modal-close" bindtap="hideCashbackModal">×</text>
    </view>
    <view class="cashback-content">
      <view class="cashback-info">
        <text class="info-text">可用余额：¥{{cashbackBalance}}</text>
        <text class="info-text">最多可抵扣：¥{{totalAmount - discountAmount}}</text>
      </view>
      <view class="cashback-input-row">
        <text class="input-label">使用金额：</text>
        <input
          class="cashback-input"
          type="digit"
          placeholder="0.00"
          value="{{cashbackUsed}}"
          bindinput="onCashbackInput"
        />
        <text class="input-unit">元</text>
      </view>
      <button class="confirm-button" bindtap="confirmCashback">确认</button>
    </view>
  </view>
</view>

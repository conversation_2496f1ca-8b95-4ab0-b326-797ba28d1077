/* 订单确认页样式 - 高端餐饮风格 */
.order-confirm-container {
  min-height: 100vh;
  background-color: #FEFEFE; /* 奶白色背景 */
  padding-bottom: 120rpx;
}

/* 桌号信息 - 优雅设计 */
.table-info {
  background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%); /* 绿色渐变 */
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 12rpx rgba(39, 174, 96, 0.2);
}

.table-text {
  color: #FFFFFF;
  font-size: 34rpx;
  font-weight: 500;
}

/* 订单商品列表 - 现代设计 */
.order-items-section {
  background: #FFFFFF;
  margin: 16rpx;
  border-radius: 12rpx; /* 统一圆角 */
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(44, 62, 80, 0.06); /* 柔和阴影 */
}

.section-title {
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #2C3E50; /* 深色文字 */
  border-bottom: 1rpx solid #ECF0F1; /* 柔和边框 */
  background-color: #F8F9FA; /* 浅色背景区分 */
}

.items-list {
  padding: 0 32rpx;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0; /* 增加间距 */
  border-bottom: 1rpx solid #ECF0F1; /* 柔和边框 */
}

.item-row:last-child {
  border-bottom: none;
}

.item-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.item-emoji {
  font-size: 48rpx; /* 增大表情符号 */
  margin-right: 24rpx;
  width: 60rpx; /* 固定宽度确保对齐 */
  text-align: center;
}

.item-details {
  display: flex;
  flex-direction: column;
  flex: 1; /* 占据剩余空间 */
}

.item-name {
  font-size: 30rpx; /* 增大字体 */
  color: #2C3E50; /* 深色文字 */
  margin-bottom: 8rpx;
  font-weight: 500;
}

.item-price {
  font-size: 26rpx;
  color: #7F8C8D; /* 柔和灰色 */
}

.item-quantity {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 120rpx; /* 确保对齐 */
}

.quantity-text {
  font-size: 28rpx;
  color: #7F8C8D; /* 柔和灰色 */
  margin-bottom: 8rpx;
}

.subtotal-text {
  font-size: 30rpx; /* 增大字体 */
  color: #27AE60; /* 主色调 */
  font-weight: 600;
}

.items-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background-color: #F8F9FA; /* 更柔和的背景 */
  border-top: 1rpx solid #ECF0F1; /* 柔和边框 */
}

.summary-text {
  font-size: 28rpx;
  color: #7F8C8D; /* 柔和灰色 */
}

.summary-amount {
  font-size: 34rpx; /* 增大字体 */
  color: #27AE60; /* 主色调 */
  font-weight: 600;
}

/* 优惠信息 - 现代设计 */
.discount-section {
  background: #FFFFFF;
  margin: 16rpx;
  border-radius: 12rpx; /* 统一圆角 */
  overflow: hidden;
  box-shadow: 0 2rpx 16rpx rgba(44, 62, 80, 0.06); /* 柔和阴影 */
}

.discount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.discount-row:last-child {
  border-bottom: none;
}

.discount-label {
  display: flex;
  flex-direction: column;
}

.label-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.available-count, .balance-text {
  font-size: 24rpx;
  color: #FF6B35;
}

.discount-value {
  display: flex;
  align-items: center;
}

.value-text {
  font-size: 28rpx;
  color: #FF6B35;
  font-weight: bold;
  margin-right: 10rpx;
}

.placeholder-text {
  font-size: 28rpx;
  color: #999;
  margin-right: 10rpx;
}

.arrow {
  font-size: 24rpx;
  color: #ccc;
}

/* 订单备注 */
.remark-section {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.remark-input {
  padding: 30rpx;
  font-size: 28rpx;
  min-height: 120rpx;
  background-color: #fafafa;
}

/* 价格明细 - 现代设计 */
.price-detail {
  background: #FFFFFF;
  margin: 16rpx;
  border-radius: 12rpx; /* 统一圆角 */
  padding: 32rpx;
  box-shadow: 0 2rpx 16rpx rgba(44, 62, 80, 0.06); /* 柔和阴影 */
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx; /* 增加间距 */
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row.final {
  padding-top: 24rpx;
  border-top: 1rpx solid #ECF0F1; /* 柔和边框 */
  margin-top: 24rpx;
}

.detail-label {
  font-size: 28rpx;
  color: #7F8C8D; /* 柔和灰色 */
}

.detail-value {
  font-size: 28rpx;
  color: #2C3E50; /* 深色文字 */
}

.detail-value.discount {
  color: #27AE60; /* 主色调 */
  font-weight: 500;
}

.detail-value.final-amount {
  font-size: 36rpx;
  color: #27AE60; /* 主色调 */
  font-weight: 600;
}

/* 支付按钮 - 高端设计 */
.pay-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  padding: 32rpx;
  border-top: 1rpx solid #ECF0F1; /* 柔和边框 */
  box-shadow: 0 -4rpx 16rpx rgba(44, 62, 80, 0.08); /* 顶部阴影 */
}

.pay-button {
  width: 100%;
  height: 96rpx; /* 增加高度 */
  background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%); /* 绿色渐变 */
  color: #FFFFFF;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 12rpx; /* 现代圆角 */
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(39, 174, 96, 0.3); /* 按钮阴影 */
  transition: all 0.3s ease;
}

.pay-button:active {
  background: linear-gradient(135deg, #229954 0%, #27AE60 100%);
  transform: translateY(1rpx);
}

.pay-button.loading {
  background: #BDC3C7; /* 柔和的加载状态 */
}

.pay-button::after {
  border: none;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: white;
  border-radius: 16rpx;
  width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 优惠券列表 */
.coupon-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.coupon-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-item:last-child {
  border-bottom: none;
}

.coupon-info {
  display: flex;
  flex-direction: column;
}

.coupon-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.coupon-desc {
  font-size: 24rpx;
  color: #666;
}

.coupon-amount {
  font-size: 32rpx;
  color: #FF6B35;
  font-weight: bold;
}

/* 返现弹窗 */
.cashback-content {
  padding: 30rpx;
}

.cashback-info {
  margin-bottom: 30rpx;
}

.info-text {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.cashback-input-row {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.cashback-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.input-unit {
  font-size: 28rpx;
  color: #666;
}

.confirm-button {
  width: 100%;
  height: 80rpx;
  background: #FF6B35;
  color: white;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;
}

.confirm-button::after {
  border: none;
}

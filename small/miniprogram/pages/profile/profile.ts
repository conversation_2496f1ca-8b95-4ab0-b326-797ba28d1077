// 个人页逻辑
interface UserInfo {
  nickName?: string;
  avatarUrl?: string;
}

interface MemberInfo {
  isMember: boolean;
  level: string;
  levelName: string;
  expireDate?: string;
}

interface Coupon {
  id: number;
  name: string;
  amount: number;
  minAmount: number;
  expireDate: string;
}

interface Order {
  id: string;
  orderNumber: string;
  tableNumber: string;
  items: any[];
  totalPrice: number;
  status: string;
  statusText: string;
  createTime: number;
  createTimeText: string;
  dishNames: string;
}

Page({
  data: {
    userInfo: {} as UserInfo,
    memberInfo: {
      isMember: true,
      level: 'gold',
      levelName: '黄金会员'
    } as MemberInfo,

    cashbackAmount: 128.50, // 返现金额
    inviteCount: 3, // 邀请人数
    showCouponModal: false, // 优惠券弹窗
    showOrderModal: false, // 订单管理弹窗
    showInviteModal: false, // 邀请记录弹窗
    inviteReward: 50, // 邀请返点金额
    
    // 优惠券数据
    coupons: [
      {
        id: 1,
        name: '新用户专享',
        amount: 10,
        minAmount: 50,
        expireDate: '2024.12.31'
      },
      {
        id: 2,
        name: '会员专享',
        amount: 20,
        minAmount: 100,
        expireDate: '2024.12.31'
      },
      {
        id: 3,
        name: '满减优惠',
        amount: 15,
        minAmount: 80,
        expireDate: '2024.11.30'
      }
    ] as Coupon[],

    allOrders: [] as Order[], // 所有订单，按时间排序，未支付置顶

    // 邀请记录数据
    inviteList: [
      {
        id: 1,
        name: '张小明',
        inviteTime: '2024-11-25 14:30',
        reward: 50
      },
      {
        id: 2,
        name: '李小红',
        inviteTime: '2024-11-20 09:15',
        reward: 50
      },
      {
        id: 3,
        name: '王小华',
        inviteTime: '2024-11-18 16:45',
        reward: 50
      }
    ]
  },

  onLoad() {
    this.loadUserInfo()
    this.loadOrders()
  },

  onShow() {
    // 每次显示页面时重新加载订单
    this.loadOrders()
  },

  // 加载用户信息
  loadUserInfo() {
    // 从本地存储获取用户信息
    let userInfo = wx.getStorageSync('userInfo') || {}

    // 如果没有用户信息，使用默认信息
    if (!userInfo.nickName) {
      userInfo = {
        nickName: '川菜达人',
        avatarUrl: '/images/default-avatar.png'
      }
    }

    this.setData({
      userInfo: userInfo
    })
  },

  // 处理登录
  handleLogin() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res)
        const userInfo = res.userInfo
        this.setData({
          userInfo: userInfo
        })
        // 保存到本地存储
        wx.setStorageSync('userInfo', userInfo)
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
      },
      fail: (error) => {
        console.log('获取用户信息失败:', error)
        wx.showToast({
          title: '登录失败',
          icon: 'error'
        })
      }
    })
  },

  // 加载订单数据
  loadOrders() {
    const orders = wx.getStorageSync('orders') || []
    const allOrders: Order[] = []

    // 处理真实订单数据
    orders.forEach((order: any, index: number) => {
      const orderData: Order = {
        id: order.id || `order_${index}`,
        orderNumber: order.orderNumber || this.generateOrderNumber(order.createTime),
        tableNumber: order.tableNumber,
        items: order.items,
        totalPrice: order.finalAmount || order.totalPrice,
        status: order.paymentStatus === 0 ? 'unpaid' : this.getOrderStatus(order.createTime),
        statusText: order.paymentStatus === 0 ? '待支付' : this.getOrderStatusText(order.createTime),
        createTime: order.createTime,
        createTimeText: this.formatTime(order.createTime),
        dishNames: order.items.map((item: any) => `${item.name}×${item.count}`).join('、')
      }

      allOrders.push(orderData)
    })

    // 如果没有订单，添加一些模拟数据
    if (allOrders.length === 0) {
      const mockOrders = this.getMockHistoryOrders()
      allOrders.push(...mockOrders)
    }

    // 排序：未支付订单置顶，然后按时间倒序
    allOrders.sort((a, b) => {
      // 未支付订单优先
      if (a.status === 'unpaid' && b.status !== 'unpaid') return -1
      if (a.status !== 'unpaid' && b.status === 'unpaid') return 1
      // 按时间倒序
      return b.createTime - a.createTime
    })

    this.setData({
      allOrders: allOrders
    })
  },

  // 获取模拟历史订单
  getMockHistoryOrders(): Order[] {
    const now = Date.now()
    return [
      {
        id: 'mock_1',
        orderNumber: '241130001',
        tableNumber: 'A03',
        items: [
          { name: '宫保鸡丁', count: 1, price: 28 },
          { name: '麻婆豆腐', count: 1, price: 18 },
          { name: '白米饭', count: 2, price: 3 }
        ],
        totalPrice: 52,
        status: 'completed',
        statusText: '已完成',
        createTime: now - 24 * 60 * 60 * 1000, // 1天前
        createTimeText: this.formatTime(now - 24 * 60 * 60 * 1000),
        dishNames: '宫保鸡丁×1、麻婆豆腐×1、白米饭×2'
      },
      {
        id: 'mock_2',
        orderNumber: '241129002',
        tableNumber: 'B05',
        items: [
          { name: '水煮鱼', count: 1, price: 45 },
          { name: '酸辣土豆丝', count: 1, price: 12 },
          { name: '鲜榨橙汁', count: 1, price: 18 }
        ],
        totalPrice: 75,
        status: 'completed',
        statusText: '已完成',
        createTime: now - 2 * 24 * 60 * 60 * 1000, // 2天前
        createTimeText: this.formatTime(now - 2 * 24 * 60 * 60 * 1000),
        dishNames: '水煮鱼×1、酸辣土豆丝×1、鲜榨橙汁×1'
      },
      {
        id: 'mock_3',
        orderNumber: '241128003',
        tableNumber: 'A01',
        items: [
          { name: '红烧肉', count: 1, price: 35 },
          { name: '凉拌黄瓜', count: 1, price: 8 },
          { name: '西红柿鸡蛋汤', count: 1, price: 15 },
          { name: '担担面', count: 1, price: 16 }
        ],
        totalPrice: 74,
        status: 'completed',
        statusText: '已完成',
        createTime: now - 3 * 24 * 60 * 60 * 1000, // 3天前
        createTimeText: this.formatTime(now - 3 * 24 * 60 * 60 * 1000),
        dishNames: '红烧肉×1、凉拌黄瓜×1、西红柿鸡蛋汤×1、担担面×1'
      }
    ]
  },

  // 生成订单号
  generateOrderNumber(createTime: number): string {
    const date = new Date(createTime)
    const year = date.getFullYear().toString().slice(-2)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    const second = date.getSeconds().toString().padStart(2, '0')
    
    return `${year}${month}${day}${hour}${minute}${second}`
  },

  // 获取订单状态
  getOrderStatus(createTime: number): string {
    const now = Date.now()
    const elapsed = now - createTime
    
    if (elapsed < 5 * 60 * 1000) { // 5分钟内
      return 'pending'
    } else if (elapsed < 20 * 60 * 1000) { // 20分钟内
      return 'cooking'
    } else {
      return 'completed'
    }
  },

  // 获取订单状态文本
  getOrderStatusText(createTime: number): string {
    const status = this.getOrderStatus(createTime)
    const statusMap = {
      'pending': '待处理',
      'cooking': '制作中',
      'completed': '已完成'
    }
    return statusMap[status as keyof typeof statusMap] || '未知状态'
  },

  // 格式化时间
  formatTime(timestamp: number): string {
    const date = new Date(timestamp)
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    const hour = date.getHours().toString().padStart(2, '0')
    const minute = date.getMinutes().toString().padStart(2, '0')
    
    return `${month}-${day} ${hour}:${minute}`
  },



  // 查看订单详情
  viewOrderDetail(e: any) {
    const order = e.currentTarget.dataset.order
    wx.showModal({
      title: `订单详情 - ${order.orderNumber}`,
      content: `桌号: ${order.tableNumber}\n菜品: ${order.dishNames}\n总价: ¥${order.totalPrice}\n状态: ${order.statusText}\n时间: ${order.createTimeText}`,
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 支付订单
  payOrder(e: any) {
    const order = e.currentTarget.dataset.order
    console.log('支付订单:', order)

    // 模拟微信支付
    wx.requestPayment({
      timeStamp: Math.floor(Date.now() / 1000).toString(),
      nonceStr: 'random_' + Math.random().toString(36).substr(2, 15),
      package: 'prepay_id=wx_prepay_' + Date.now(),
      signType: 'RSA',
      paySign: 'mock_sign_' + Date.now(),
      success: (res) => {
        console.log('支付成功:', res)
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        })

        // 更新订单状态
        const orders = wx.getStorageSync('orders') || []
        const updatedOrders = orders.map((item: any) => {
          if (item.id === order.id) {
            return { ...item, paymentStatus: 1, status: 'cooking' }
          }
          return item
        })
        wx.setStorageSync('orders', updatedOrders)

        // 重新加载订单
        this.loadOrders()
      },
      fail: (err) => {
        console.error('支付失败:', err)
        wx.showToast({
          title: '支付失败',
          icon: 'error'
        })
      }
    })
  },

  // 显示优惠券
  showCoupons() {
    this.setData({
      showCouponModal: true
    })
  },

  // 隐藏优惠券弹窗
  hideCouponModal() {
    this.setData({
      showCouponModal: false
    })
  },

  // 显示订单管理弹窗
  showOrderModal() {
    this.setData({
      showOrderModal: true
    })
  },

  // 隐藏订单管理弹窗
  hideOrderModal() {
    this.setData({
      showOrderModal: false
    })
  },

  // 显示邀请记录
  showInviteRecord() {
    this.setData({
      showInviteModal: true
    })
  },

  // 隐藏邀请记录弹窗
  hideInviteModal() {
    this.setData({
      showInviteModal: false
    })
  },

  // 显示关于我们
  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: '川味小厨 - 正宗川菜，地道口味\n\n联系电话：400-123-4567\n地址：成都市锦江区春熙路123号',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 编辑个人资料
  editProfile() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 邀请好友
  inviteFriends() {
    wx.showModal({
      title: '邀请好友',
      content: `邀请好友注册会员，您可获得¥${this.data.inviteReward}返点奖励！\n\n分享给好友，让他们也享受美味吧！`,
      confirmText: '立即分享',
      success: (res) => {
        if (res.confirm) {
          wx.showShareMenu({
            withShareTicket: true,
            success: () => {
              wx.showToast({
                title: '分享成功',
                icon: 'success'
              })
            }
          })
        }
      }
    })
  },

  // 购买会员
  buyMembership() {
    wx.showModal({
      title: '购买会员',
      content: '成为会员享受以下特权：\n• 专属优惠券\n• 积分返现\n• 生日特惠\n• 优先服务\n\n会员费用：¥99/年',
      confirmText: '立即购买',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用支付接口
          wx.showToast({
            title: '购买成功！',
            icon: 'success'
          })
          
          // 更新会员状态
          this.setData({
            'memberInfo.isMember': true,
            'memberInfo.level': 'gold',
            'memberInfo.levelName': '黄金会员',
            'memberInfo.expireDate': '2025.12.31'
          })
        }
      }
    })
  },

  // 清除数据
  clearData() {
    wx.showModal({
      title: '确认清除',
      content: '这将清除所有本地数据，包括购物车、订单记录等，确定要继续吗？',
      success: (res) => {
        if (res.confirm) {
          wx.clearStorageSync()
          this.setData({
            currentOrders: [],
            historyOrders: [],
            userInfo: {}
          })
          wx.showToast({
            title: '数据已清除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 关于我们
  about() {
    wx.showModal({
      title: '关于美味餐厅',
      content: '美味餐厅小程序 v1.0\n\n专注于为您提供优质的用餐体验\n正宗川菜，地道美味\n\n联系我们：123-4567-8890',
      showCancel: false,
      confirmText: '知道了'
    })
  }
})

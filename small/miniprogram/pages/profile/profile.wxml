<!--个人页-->
<view class="profile-container">
  <!-- 用户信息 -->
  <view class="user-info card">
    <view class="user-main">
      <view class="user-avatar" bindtap="editProfile">
        <text class="avatar-placeholder">👤</text>
      </view>
      <view class="user-details" bindtap="editProfile">
        <text class="user-name">{{userInfo.nickName || '点击登录'}}</text>
        <text class="member-level {{memberInfo.level}}">{{memberInfo.levelName}}</text>
      </view>
    </view>
    <view class="user-stats">
      <view class="stat-item">
        <text class="stat-value">{{coupons.length || 0}}</text>
        <text class="stat-label">优惠券</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-value">¥{{inviteReward * inviteCount || 0}}</text>
        <text class="stat-label">返现金额</text>
      </view>
    </view>
    <view class="login-btn" bindtap="handleLogin" wx:if="{{!userInfo.nickName}}">
      <text>登录</text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="showCoupons">
      <view class="menu-icon">💰</view>
      <text class="menu-title">我的优惠券</text>
      <text class="menu-arrow">></text>
    </view>

    <view class="menu-item" bindtap="showOrderModal">
      <view class="menu-icon">📋</view>
      <text class="menu-title">订单管理</text>
      <text class="menu-arrow">></text>
    </view>

    <view class="menu-item" bindtap="showInviteRecord">
      <view class="menu-icon">👥</view>
      <text class="menu-title">邀请记录</text>
      <text class="menu-arrow">></text>
    </view>

    <view class="menu-item" bindtap="showAbout">
      <view class="menu-icon">ℹ️</view>
      <text class="menu-title">关于我们</text>
      <text class="menu-arrow">></text>
    </view>
  </view>

</view>

<!-- 优惠券弹窗 -->
<view class="coupon-modal {{showCouponModal ? 'show' : ''}}" bindtap="hideCouponModal">
  <view class="coupon-content" catchtap="stopPropagation">
    <view class="coupon-header">
      <text class="coupon-title">我的优惠券</text>
      <view class="close-btn" bindtap="hideCouponModal">×</view>
    </view>
    <scroll-view class="coupon-list" scroll-y="true">
      <view class="coupon-item" wx:for="{{coupons}}" wx:key="id">
        <view class="coupon-left">
          <text class="coupon-amount">¥{{item.amount}}</text>
          <text class="coupon-condition">满{{item.minAmount}}可用</text>
        </view>
        <view class="coupon-right">
          <text class="coupon-name">{{item.name}}</text>
          <text class="coupon-expire">有效期至: {{item.expireDate}}</text>
        </view>
      </view>

      <view class="empty-state" wx:if="{{coupons.length === 0}}">
        <text class="empty-icon">💰</text>
        <text>暂无优惠券</text>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 订单管理弹窗 -->
<view class="order-modal {{showOrderModal ? 'show' : ''}}" bindtap="hideOrderModal">
  <view class="order-content" catchtap="stopPropagation">
    <view class="order-header">
      <text class="order-title">订单管理</text>
      <view class="close-btn" bindtap="hideOrderModal">×</view>
    </view>

    <scroll-view class="order-list" scroll-y="true">
      <view class="order-item" wx:for="{{allOrders}}" wx:key="id" bindtap="viewOrderDetail" data-order="{{item}}">
        <view class="order-header">
          <text class="order-number">订单号: {{item.orderNumber}}</text>
          <text class="order-status {{item.status}}">{{item.statusText}}</text>
        </view>
        <view class="order-info">
          <text class="order-table">{{item.tableNumber}}号桌</text>
          <text class="order-time">{{item.createTimeText}}</text>
        </view>
        <view class="order-items">
          <text class="order-dishes">{{item.dishNames}}</text>
          <text class="order-price price">¥{{item.totalPrice}}</text>
          <!-- 未支付订单显示支付按钮 -->
          <button wx:if="{{item.status === 'unpaid'}}" class="pay-button" size="mini" type="primary" catchtap="payOrder" data-order="{{item}}">去支付</button>
        </view>
      </view>

      <view class="empty-state" wx:if="{{allOrders.length === 0}}">
        <text class="empty-icon">📋</text>
        <text>暂无订单</text>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 邀请记录弹窗 -->
<view class="invite-modal {{showInviteModal ? 'show' : ''}}" bindtap="hideInviteModal">
  <view class="invite-content" catchtap="stopPropagation">
    <view class="invite-header">
      <text class="invite-title">邀请记录</text>
      <view class="close-btn" bindtap="hideInviteModal">×</view>
    </view>
    <scroll-view class="invite-list" scroll-y="true">
      <view class="invite-item" wx:for="{{inviteList}}" wx:key="id">
        <view class="invite-avatar">
          <text class="invite-avatar-text">{{item.name.charAt(0)}}</text>
        </view>
        <view class="invite-info">
          <text class="invite-name">{{item.name}}</text>
          <text class="invite-time">{{item.inviteTime}}</text>
        </view>
      </view>

      <view class="empty-state" wx:if="{{inviteList.length === 0}}">
        <text class="empty-icon">👥</text>
        <text>暂无邀请记录</text>
      </view>
    </scroll-view>
  </view>
</view>

<!-- 自定义底部导航栏 - 临时隐藏测试原生tabBar -->
<!-- <gradient-tabbar
  current-tab="{{2}}"
  theme="glass"
  bind:tabchange="onTabChange"
></gradient-tabbar> -->

// 餐饮小程序工具类

/**
 * 格式化时间
 */
export const formatTime = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return (
    [year, month, day].map(formatNumber).join('/') +
    ' ' +
    [hour, minute, second].map(formatNumber).join(':')
  )
}

const formatNumber = (n: number) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

/**
 * 格式化价格
 */
export const formatPrice = (price: number): string => {
  return `¥${price.toFixed(2)}`
}

/**
 * 生成订单号
 */
export const generateOrderNumber = (): string => {
  const now = new Date()
  const year = now.getFullYear().toString().slice(-2)
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')

  return `${year}${month}${day}${hour}${minute}${second}${random}`
}

/**
 * 存储管理
 */
export const storage = {
  // 设置存储
  set(key: string, value: any): void {
    try {
      wx.setStorageSync(key, value)
    } catch (error) {
      console.error('存储失败:', error)
    }
  },

  // 获取存储
  get(key: string, defaultValue: any = null): any {
    try {
      return wx.getStorageSync(key) || defaultValue
    } catch (error) {
      console.error('获取存储失败:', error)
      return defaultValue
    }
  },

  // 删除存储
  remove(key: string): void {
    try {
      wx.removeStorageSync(key)
    } catch (error) {
      console.error('删除存储失败:', error)
    }
  },

  // 清空存储
  clear(): void {
    try {
      wx.clearStorageSync()
    } catch (error) {
      console.error('清空存储失败:', error)
    }
  }
}

/**
 * 显示提示信息
 */
export const showToast = (title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none') => {
  wx.showToast({
    title,
    icon,
    duration: 2000
  })
}

/**
 * 显示确认对话框
 */
export const showConfirm = (title: string, content: string): Promise<boolean> => {
  return new Promise((resolve) => {
    wx.showModal({
      title,
      content,
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}
